import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and API auth routes
  if (
    pathname.startsWith("/_next") ||
    pathname.startsWith("/api/auth") ||
    pathname.includes(".")
  ) {
    return NextResponse.next();
  }

  // Get session
  const session = await auth.api.getSession({
    headers: request.headers,
  });

  // Protected routes that require authentication
  const protectedRoutes = ["/dashboard", "/cart", "/checkout", "/orders", "/profile"];
  const adminRoutes = ["/admin"];

  // Check if route requires authentication
  const requiresAuth = protectedRoutes.some(route => pathname.startsWith(route));
  const requiresAdmin = adminRoutes.some(route => pathname.startsWith(route));

  // Redirect to sign-in if not authenticated and route requires auth
  if (requiresAuth && !session) {
    const signInUrl = new URL("/sign-in", request.url);
    signInUrl.searchParams.set("callbackUrl", pathname);
    return NextResponse.redirect(signInUrl);
  }

  // Redirect to dashboard if not admin and route requires admin
  if (requiresAdmin && (!session || session.user.role !== "ADMIN")) {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // API route protection
  if (pathname.startsWith("/api/")) {
    // Admin API routes
    if (pathname.startsWith("/api/admin/")) {
      if (!session || session.user.role !== "ADMIN") {
        return NextResponse.json(
          { error: "Unauthorized - Admin access required" },
          { status: 403 }
        );
      }
    }

    // Protected API routes (require any authentication)
    const protectedApiRoutes = [
      "/api/cart",
      "/api/orders",
      "/api/profile",
      "/api/reviews",
    ];

    const requiresApiAuth = protectedApiRoutes.some(route => 
      pathname.startsWith(route)
    );

    if (requiresApiAuth && !session) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 }
      );
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public/).*)",
  ],
};
