import { User } from "@/utils/types";
import React, { useEffect, useState } from "react";
import { Separator } from "../ui/separator";
import ShoeCards from "../shoeCards";

type Props = {
  user: User;
};

const sliderImages = [
  "https://olvjn20z71.ufs.sh/f/c3xIsFCNfOyD6JymmJMsV3Hen1ZldCBumR0x2kcQ8MK4DOFG",
  "https://olvjn20z71.ufs.sh/f/c3xIsFCNfOyDPHEkIDuL69zypGmsc1ioZSnTAYbKjUxlWDv0",
  "https://olvjn20z71.ufs.sh/f/c3xIsFCNfOyDIyTFoLSyvoW0HYM2Vd7DcfPQT1q8FO4AZCls",
];

const discountedShoes = [
  {
    id: "1",
    name: "Nike Air Max 270",
    image: sliderImages[0],
    discountedPrice: 180,
    price: 120,
    discount: true,
    rating: 2.5,
    numberOfRatings: 45,
  },
  {
    id: "2",
    name: "Adidas Ultraboost",
    image: sliderImages[1],
    discountedPrice: 200,
    price: 140,
    discount: true,
    rating: 5,
    numberOfRatings: 10,
  },
  {
    id: "3",
    name: "Puma RS-X",
    image: sliderImages[2],
    discountedPrice: 150,
    price: 99,
    discount: true,
    rating: 4.5,
    numberOfRatings: 60,
  },
];

const shoes = [
  {
    id: "4",
    name: "Nike Air Max 270",
    image: sliderImages[0],
    discountedPrice: 180,
    price: 120,
    discount: false,
    rating: 2.5,
    numberOfRatings: 45,
  },
  {
    id: "5",
    name: "Adidas Ultraboost",
    image: sliderImages[1],
    discountedPrice: 200,
    price: 140,
    discount: false,
    rating: 5,
    numberOfRatings: 10,
  },
  {
    id: "6",
    name: "Puma RS-X",
    image: sliderImages[2],
    discountedPrice: 150,
    price: 99,
    discount: false,
    rating: 4.5,
    numberOfRatings: 60,
  },
];

const Content = (User: Props) => {
  const [current, setCurrent] = useState(0);

  //   const prevSlide = () =>
  //     setCurrent((prev) => (prev === 0 ? sliderImages.length - 1 : prev - 1));
  //   const nextSlide = () =>
  //     setCurrent((prev) => (prev === sliderImages.length - 1 ? 0 : prev + 1));

  useEffect(() => {
    const nextSlide = setInterval(() => {
      setCurrent((prev) => (prev === sliderImages.length - 1 ? 0 : prev + 1));
    }, 3000); // Change slide every 5 seconds

    return () => clearInterval(nextSlide); // Cleanup interval on unmount
  }, []);

  return (
    <div className="w-full h-full p-4">
      <h1 className="text-xl mb-5">
        Hey, <span className="">{User.user.name}</span>!
      </h1>
      <div className="w-full flex items-center justify-center">
        <div className="border rounded-lg w-[95%] md:[90%] flex flex-col items-center p-4 bg-white">
          <div className="relative w-full flex items-center justify-center h-[28rem] rounded-lg overflow-hidden">
            {sliderImages.map((img, idx) => (
              <img
                key={img}
                src={img}
                alt={`slide-${idx}`}
                className={`absolute top-0 left-0 w-full h-full object-cover transition-opacity duration-1000 ${
                  idx === current ? "opacity-100 z-10" : "opacity-0 z-0"
                }`}
                style={{ borderRadius: "0.5rem" }}
              />
            ))}
          </div>
        </div>
      </div>
      <div className="flex w-[95%] md:[90%] mx-auto items-center gap-2 justify-between my-4 overflow-hidden">
        <h2 className="text-md">Deals</h2>
        <Separator className="" />
      </div>

      {/* Discounted Shoes Section */}
      <div className="w-[95%] mx-auto md:mx-0 md:w-[90%] md:pl-10 flex flex-wrap flex-col md:flex-row gap-4 items-center">
        <ShoeCards shoes={discountedShoes} />
      </div>
      <div className="flex w-[95%] md:[90%] mx-auto items-center gap-2 justify-between my-4 overflow-hidden">
        <h2 className="text-md">All Products</h2>
        <Separator className="" />
      </div>
      <div className="w-[95%] mx-auto md:mx-0 md:w-[90%] md:pl-10 flex flex-wrap flex-col md:flex-row gap-4 items-center">
        <ShoeCards shoes={shoes} />
      </div>
    </div>
  );
};

export default Content;
