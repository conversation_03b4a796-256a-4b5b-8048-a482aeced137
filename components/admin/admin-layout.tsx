"use client";

import { User } from "@/utils/types";
import AdminSidebar from "./admin-sidebar";
import AdminHeader from "./admin-header";

interface AdminLayoutProps {
  user: User;
  children: React.ReactNode;
}

export default function AdminLayout({ user, children }: AdminLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <AdminHeader user={user} />
      <div className="flex">
        <AdminSidebar />
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
