"use client";

import { useState, useEffect } from "react";
import { ContactMessage, ContactMessageStatus } from "@/utils/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  Mail, 
  Search, 
  Filter, 
  Eye, 
  MessageSquare, 
  CheckCircle, 
  Clock,
  User,
  Calendar
} from "lucide-react";
import Link from "next/link";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";

export default function ContactMessagesContent() {
  const [messages, setMessages] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  const fetchMessages = async (page = 1, status = "all", search = "") => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
      });

      if (status !== "all") {
        params.set("status", status);
      }

      if (search.trim()) {
        params.set("search", search.trim());
      }

      const response = await fetch(`/api/contact?${params.toString()}`);
      const result = await response.json();

      if (result.success) {
        setMessages(result.data.data);
        setPagination(result.data.pagination);
      } else {
        setError(result.error || "Failed to fetch messages");
      }
    } catch (err) {
      setError("Failed to fetch messages");
      console.error("Error fetching messages:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMessages(1, statusFilter, searchTerm);
  }, [statusFilter]);

  const handleSearch = () => {
    fetchMessages(1, statusFilter, searchTerm);
  };

  const handlePageChange = (page: number) => {
    fetchMessages(page, statusFilter, searchTerm);
  };

  const getStatusIcon = (status: ContactMessageStatus) => {
    switch (status) {
      case "UNREAD":
        return <Mail className="h-4 w-4" />;
      case "READ":
        return <Eye className="h-4 w-4" />;
      case "REPLIED":
        return <MessageSquare className="h-4 w-4" />;
      case "RESOLVED":
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: ContactMessageStatus) => {
    switch (status) {
      case "UNREAD":
        return "bg-red-100 text-red-800";
      case "READ":
        return "bg-blue-100 text-blue-800";
      case "REPLIED":
        return "bg-purple-100 text-purple-800";
      case "RESOLVED":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getSubjectLabel = (subject: string) => {
    const subjectMap: Record<string, string> = {
      general: "General Inquiry",
      order: "Order Support",
      product: "Product Question",
      shipping: "Shipping & Delivery",
      returns: "Returns & Refunds",
      technical: "Technical Support",
      partnership: "Business Partnership",
      feedback: "Feedback & Suggestions",
    };
    return subjectMap[subject] || subject;
  };

  if (loading && messages.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <SpinnerCircle4 />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Contact Messages</h1>
          <p className="text-gray-600">Manage customer inquiries and support requests</p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search messages..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Messages</SelectItem>
                <SelectItem value="UNREAD">Unread</SelectItem>
                <SelectItem value="READ">Read</SelectItem>
                <SelectItem value="REPLIED">Replied</SelectItem>
                <SelectItem value="RESOLVED">Resolved</SelectItem>
              </SelectContent>
            </Select>
            
            <Button onClick={handleSearch}>
              <Filter className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Messages List */}
      {error && (
        <div className="text-center py-8">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => fetchMessages(1, statusFilter, searchTerm)}>
            Try Again
          </Button>
        </div>
      )}

      {messages.length === 0 && !loading && !error ? (
        <div className="text-center py-12">
          <Mail className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">No messages found</h2>
          <p className="text-gray-600">
            {statusFilter === "all" 
              ? "No contact messages have been received yet." 
              : `No messages with status "${statusFilter}" found.`}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {messages.map((message) => (
            <Card key={message.id} className={message.status === "UNREAD" ? "border-l-4 border-l-red-500" : ""}>
              <CardContent className="pt-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold text-gray-900">
                        {getSubjectLabel(message.subject)}
                      </h3>
                      <Badge className={getStatusColor(message.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(message.status)}
                          {message.status}
                        </div>
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {message.name}
                      </div>
                      <div className="flex items-center gap-1">
                        <Mail className="h-4 w-4" />
                        {message.email}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {new Date(message.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                    
                    <p className="text-gray-700 line-clamp-2">
                      {message.message}
                    </p>
                    
                    {message.user && (
                      <div className="mt-2 text-xs text-blue-600">
                        Registered user: {message.user.name}
                      </div>
                    )}
                  </div>
                  
                  <div className="ml-4">
                    <Link href={`/admin/contact-messages/${message.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-8">
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page <= 1 || loading}
              >
                Previous
              </Button>
              
              <span className="flex items-center px-4 py-2 text-sm text-gray-600">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page >= pagination.totalPages || loading}
              >
                Next
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
