"use client";

import { Product } from "@/utils/types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Star, User } from "lucide-react";
import { useState } from "react";

interface ProductTabsProps {
  product: Product;
}

export default function ProductTabs({ product }: ProductTabsProps) {
  const [newReview, setNewReview] = useState({ rating: 5, comment: "" });

  const renderStars = (rating: number, interactive = false, onRatingChange?: (rating: number) => void) => {
    const stars = [];
    
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <button
          key={i}
          type="button"
          className={`${interactive ? "cursor-pointer hover:scale-110" : ""} transition-transform`}
          onClick={() => interactive && onRatingChange?.(i)}
          disabled={!interactive}
        >
          <Star 
            className={`h-5 w-5 ${
              i <= rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
            }`} 
          />
        </button>
      );
    }
    
    return stars;
  };

  const handleSubmitReview = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement review submission
    console.log("Submit review:", newReview);
    setNewReview({ rating: 5, comment: "" });
  };

  return (
    <Tabs defaultValue="details" className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="details">Details</TabsTrigger>
        <TabsTrigger value="reviews">Reviews ({product.reviewCount})</TabsTrigger>
        <TabsTrigger value="shipping">Shipping & Returns</TabsTrigger>
      </TabsList>

      <TabsContent value="details" className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Product Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Description</h4>
              <p className="text-gray-600 leading-relaxed">
                {product.description || "No description available."}
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Specifications</h4>
                <dl className="space-y-2">
                  <div className="flex justify-between">
                    <dt className="text-gray-600">Brand:</dt>
                    <dd className="font-medium">{product.brand}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-gray-600">Category:</dt>
                    <dd className="font-medium">{product.category.name}</dd>
                  </div>
                  {product.sizes.length > 0 && (
                    <div className="flex justify-between">
                      <dt className="text-gray-600">Available Sizes:</dt>
                      <dd className="font-medium">{product.sizes.join(", ")}</dd>
                    </div>
                  )}
                  {product.colors.length > 0 && (
                    <div className="flex justify-between">
                      <dt className="text-gray-600">Available Colors:</dt>
                      <dd className="font-medium">{product.colors.join(", ")}</dd>
                    </div>
                  )}
                </dl>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Features</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• Premium quality materials</li>
                  <li>• Comfortable fit</li>
                  <li>• Durable construction</li>
                  <li>• Easy to clean</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="reviews" className="mt-6">
        <div className="space-y-6">
          {/* Reviews Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Reviews</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4 mb-6">
                <div className="text-center">
                  <div className="text-3xl font-bold">{product.rating.toFixed(1)}</div>
                  <div className="flex items-center justify-center mb-1">
                    {renderStars(product.rating)}
                  </div>
                  <div className="text-sm text-gray-500">{product.reviewCount} reviews</div>
                </div>
                
                <div className="flex-1">
                  {[5, 4, 3, 2, 1].map((stars) => (
                    <div key={stars} className="flex items-center gap-2 mb-1">
                      <span className="text-sm w-3">{stars}</span>
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-yellow-400 h-2 rounded-full" 
                          style={{ width: `${Math.random() * 100}%` }}
                        />
                      </div>
                      <span className="text-sm text-gray-500 w-8">
                        {Math.floor(Math.random() * 20)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Write a Review */}
          <Card>
            <CardHeader>
              <CardTitle>Write a Review</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmitReview} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Rating</label>
                  <div className="flex items-center gap-1">
                    {renderStars(newReview.rating, true, (rating) => 
                      setNewReview(prev => ({ ...prev, rating }))
                    )}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Comment</label>
                  <Textarea
                    placeholder="Share your thoughts about this product..."
                    value={newReview.comment}
                    onChange={(e) => setNewReview(prev => ({ ...prev, comment: e.target.value }))}
                    rows={4}
                  />
                </div>
                
                <Button type="submit">Submit Review</Button>
              </form>
            </CardContent>
          </Card>

          {/* Reviews List */}
          <div className="space-y-4">
            {product.reviews?.map((review) => (
              <Card key={review.id}>
                <CardContent className="pt-6">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-gray-500" />
                      </div>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium">{review.userName}</span>
                        <div className="flex items-center">
                          {renderStars(review.rating)}
                        </div>
                        <span className="text-sm text-gray-500">
                          {new Date(review.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                      
                      {review.comment && (
                        <p className="text-gray-600">{review.comment}</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </TabsContent>

      <TabsContent value="shipping" className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Shipping & Returns</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h4 className="font-semibold mb-2">Shipping Information</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• Free standard shipping on orders over M 500</li>
                <li>• Standard delivery: 3-5 business days</li>
                <li>• Express delivery: 1-2 business days (additional charges apply)</li>
                <li>• Same-day delivery available in selected areas</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Return Policy</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• 30-day return window from delivery date</li>
                <li>• Items must be in original condition with tags</li>
                <li>• Free returns for defective or incorrect items</li>
                <li>• Return shipping costs apply for change of mind returns</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Warranty</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• 2-year manufacturer warranty</li>
                <li>• Covers manufacturing defects</li>
                <li>• Does not cover normal wear and tear</li>
                <li>• Warranty claim process available online</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
}
