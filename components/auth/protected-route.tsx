"use client";

import { useSession } from "@/lib/auth-client";
import { UserRole } from "@/utils/types";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircleIcon } from "lucide-react";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  fallbackUrl?: string;
}

export function ProtectedRoute({ 
  children, 
  requiredRole, 
  fallbackUrl = "/sign-in" 
}: ProtectedRouteProps) {
  const { data: session, isPending, error } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (!isPending && !session) {
      router.push(fallbackUrl);
    }
    
    if (session && requiredRole && session.user.role !== requiredRole) {
      // If user doesn't have required role, redirect to dashboard
      router.push("/dashboard");
    }
  }, [session, isPending, requiredRole, router, fallbackUrl]);

  if (isPending) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <Alert variant="destructive">
          <AlertCircleIcon />
          <AlertTitle>Authentication Error</AlertTitle>
          <AlertDescription>
            <p>{error.message}</p>
            <p>Please try again. If error persists, please contact support</p>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (requiredRole && session.user.role !== requiredRole && session.user.role !== UserRole.ADMIN) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <Alert variant="destructive">
          <AlertCircleIcon />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            <p>You don't have permission to access this page.</p>
            <p>Required role: {requiredRole}</p>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return <>{children}</>;
}

// Convenience components for specific roles
export function AdminRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      {children}
    </ProtectedRoute>
  );
}

export function UserRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute>
      {children}
    </ProtectedRoute>
  );
}
