"use client";

import { CartItem as CartItemType } from "@/contexts/cart-context";
import { useCart } from "@/contexts/cart-context";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Minus, Plus, Trash2, Heart } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { formatPrice, getEffectivePrice, isProductOnSale, calculateDiscountPercentage } from "@/lib/product-utils";

interface CartItemProps {
  item: CartItemType;
}

export default function CartItem({ item }: CartItemProps) {
  const { updateQuantity, removeItem } = useCart();
  const [imageError, setImageError] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const { product, quantity, size, color } = item;
  const effectivePrice = getEffectivePrice(product);
  const isOnSale = isProductOnSale(product);
  const discountPercentage = isOnSale 
    ? calculateDiscountPercentage(product.price, product.discountedPrice!)
    : 0;

  const handleQuantityChange = async (newQuantity: number) => {
    if (newQuantity < 1 || newQuantity > product.stock || isUpdating) return;
    
    setIsUpdating(true);
    try {
      updateQuantity(item.id, newQuantity);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemove = () => {
    if (window.confirm("Remove this item from your cart?")) {
      removeItem(item.id);
    }
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex gap-4">
          {/* Product Image */}
          <div className="flex-shrink-0">
            <Link href={`/products/${product.id}`}>
              <div className="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden">
                {!imageError && product.images.length > 0 ? (
                  <img
                    src={product.images[0]}
                    alt={product.name}
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gray-200">
                    <div className="text-gray-400 text-center">
                      <svg
                        className="mx-auto h-8 w-8 mb-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
            </Link>
          </div>

          {/* Product Details */}
          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-start mb-2">
              <div>
                <Link 
                  href={`/products/${product.id}`}
                  className="font-semibold text-gray-900 hover:text-blue-600 transition-colors"
                >
                  {product.name}
                </Link>
                <p className="text-sm text-gray-500">{product.brand}</p>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRemove}
                  className="text-gray-400 hover:text-red-500"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Variants */}
            <div className="flex gap-2 mb-3">
              {size && (
                <Badge variant="outline" className="text-xs">
                  Size: {size}
                </Badge>
              )}
              {color && (
                <Badge variant="outline" className="text-xs">
                  Color: {color}
                </Badge>
              )}
              {isOnSale && (
                <Badge variant="destructive" className="text-xs">
                  -{discountPercentage}% OFF
                </Badge>
              )}
            </div>

            {/* Price and Quantity */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="font-semibold text-lg">
                  {formatPrice(effectivePrice)}
                </span>
                {isOnSale && (
                  <span className="text-sm text-gray-500 line-through">
                    {formatPrice(product.price)}
                  </span>
                )}
              </div>

              {/* Quantity Controls */}
              <div className="flex items-center gap-3">
                <div className="flex items-center border rounded-lg">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1 || isUpdating}
                    className="h-8 w-8 p-0"
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  
                  <span className="px-3 py-1 font-medium min-w-[2rem] text-center">
                    {isUpdating ? "..." : quantity}
                  </span>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleQuantityChange(quantity + 1)}
                    disabled={quantity >= product.stock || isUpdating}
                    className="h-8 w-8 p-0"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                <div className="text-right">
                  <div className="font-semibold">
                    {formatPrice(effectivePrice * quantity)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatPrice(effectivePrice)} each
                  </div>
                </div>
              </div>
            </div>

            {/* Stock Warning */}
            {quantity >= product.stock && (
              <div className="mt-2 text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                Maximum available quantity reached
              </div>
            )}

            {/* Low Stock Warning */}
            {product.stock <= 5 && product.stock > 0 && (
              <div className="mt-2 text-xs text-orange-600">
                Only {product.stock} left in stock
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
