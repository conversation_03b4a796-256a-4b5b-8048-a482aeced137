"use client";

import { useSession } from "@/lib/auth-client";
import { UserRole } from "@/utils/types";

export function useRole() {
  const { data: session, isPending } = useSession();

  const hasRole = (requiredRole: UserRole): boolean => {
    if (!session?.user) return false;
    
    // Admin has access to everything
    if (session.user.role === UserRole.ADMIN) return true;
    
    // Check specific role
    return session.user.role === requiredRole;
  };

  const isAdmin = (): boolean => {
    return session?.user?.role === UserRole.ADMIN || false;
  };

  const isUser = (): boolean => {
    return session?.user?.role === UserRole.USER || false;
  };

  const getUserRole = (): UserRole | null => {
    return session?.user?.role || null;
  };

  const isAuthenticated = (): boolean => {
    return !!session?.user;
  };

  return {
    hasRole,
    isAdmin,
    isUser,
    getUserRole,
    isAuthenticated,
    isPending,
    user: session?.user || null,
  };
}
