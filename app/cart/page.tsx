"use client";

import { useSession } from "@/lib/auth-client";
import { UserRoute } from "@/components/auth/protected-route";
import NavBar from "@/components/navbar";
import CartContent from "@/components/cart/cart-content";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import { User } from "@/utils/types";

export default function CartPage() {
  return (
    <UserRoute>
      <CartPageContent />
    </UserRoute>
  );
}

function CartPageContent() {
  const { data: session } = useSession();

  // At this point, we know the user is authenticated due to UserRoute
  // Type cast to include the role property that exists at runtime
  const user = session!.user as User;

  return (
    <div className="min-h-screen bg-gray-50">
      <NavBar user={user} />
      <div className="container mx-auto px-4 py-6">
        <CartContent />
      </div>
    </div>
  );
}
