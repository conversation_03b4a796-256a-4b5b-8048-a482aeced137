"use client";

import { useSession } from "@/lib/auth-client";
import { AdminRoute } from "@/components/auth/protected-route";
import AdminLayout from "@/components/admin/admin-layout";
import ContactMessagesContent from "@/components/admin/contact-messages/contact-messages-content";

export default function AdminContactMessagesPage() {
  return (
    <AdminRoute>
      <AdminContactMessagesPageContent />
    </AdminRoute>
  );
}

function AdminContactMessagesPageContent() {
  const { data: session } = useSession();

  // At this point, we know the user is authenticated and is an admin due to AdminRoute
  const user = session!.user;

  return (
    <AdminLayout user={user}>
      <ContactMessagesContent />
    </AdminLayout>
  );
}
