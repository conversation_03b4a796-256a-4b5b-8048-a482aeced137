"use client";

import { useSession } from "@/lib/auth-client";
import { AdminRoute } from "@/components/auth/protected-route";
import AdminLayout from "@/components/admin/admin-layout";
import ContactMessageDetail from "@/components/admin/contact-messages/contact-message-detail";
import { useParams } from "next/navigation";

export default function AdminContactMessageDetailPage() {
  return (
    <AdminRoute>
      <AdminContactMessageDetailPageContent />
    </AdminRoute>
  );
}

function AdminContactMessageDetailPageContent() {
  const { data: session } = useSession();
  const params = useParams();
  const messageId = params.id as string;

  // At this point, we know the user is authenticated and is an admin due to AdminRoute
  const user = session!.user;

  return (
    <AdminLayout user={user}>
      <ContactMessageDetail messageId={messageId} />
    </AdminLayout>
  );
}
