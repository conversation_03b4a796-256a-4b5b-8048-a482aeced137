import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/lib/generated/prisma";
import { ProductFilters, ApiResponse, PaginatedResponse } from "@/utils/types";

const prisma = new PrismaClient();

// GET /api/products - Get products with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const filters: ProductFilters = {
      search: searchParams.get("search") || undefined,
      categoryId: searchParams.get("categoryId") || undefined,
      brand: searchParams.get("brand") || undefined,
      minPrice: searchParams.get("minPrice") ? parseFloat(searchParams.get("minPrice")!) : undefined,
      maxPrice: searchParams.get("maxPrice") ? parseFloat(searchParams.get("maxPrice")!) : undefined,
      sizes: searchParams.get("sizes") ? searchParams.get("sizes")!.split(",") : undefined,
      colors: searchParams.get("colors") ? searchParams.get("colors")!.split(",") : undefined,
      sortBy: (searchParams.get("sortBy") as any) || "name",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "asc",
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "12"),
    };

    // Build where clause
    const where: any = {
      isActive: true,
    };

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: "insensitive" } },
        { description: { contains: filters.search, mode: "insensitive" } },
        { brand: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    if (filters.categoryId) {
      where.categoryId = filters.categoryId;
    }

    if (filters.brand) {
      where.brand = { contains: filters.brand, mode: "insensitive" };
    }

    if (filters.minPrice || filters.maxPrice) {
      where.price = {};
      if (filters.minPrice) where.price.gte = filters.minPrice;
      if (filters.maxPrice) where.price.lte = filters.maxPrice;
    }

    if (filters.sizes && filters.sizes.length > 0) {
      where.sizes = { hasSome: filters.sizes };
    }

    if (filters.colors && filters.colors.length > 0) {
      where.colors = { hasSome: filters.colors };
    }

    // Build orderBy clause
    let orderBy: any = {};
    switch (filters.sortBy) {
      case "price":
        orderBy.price = filters.sortOrder;
        break;
      case "rating":
        orderBy.rating = filters.sortOrder;
        break;
      case "newest":
        orderBy.createdAt = "desc";
        break;
      default:
        orderBy.name = filters.sortOrder;
    }

    // Calculate pagination
    const skip = (filters.page! - 1) * filters.limit!;

    // Get total count
    const total = await prisma.product.count({ where });

    // Get products
    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        _count: {
          select: { reviews: true }
        }
      },
      orderBy,
      skip,
      take: filters.limit,
    });

    const response: ApiResponse<PaginatedResponse<typeof products[0]>> = {
      success: true,
      data: {
        data: products,
        pagination: {
          page: filters.page!,
          limit: filters.limit!,
          total,
          totalPages: Math.ceil(total / filters.limit!),
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch products" },
      { status: 500 }
    );
  }
}
