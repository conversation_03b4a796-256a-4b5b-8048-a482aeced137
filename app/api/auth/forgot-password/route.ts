import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/lib/generated/prisma";
import { ApiResponse } from "@/utils/types";
import { sendPasswordResetEmail } from "@/lib/email-service";
import crypto from "crypto";

const prisma = new PrismaClient();

// POST /api/auth/forgot-password - Send password reset email
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    // Validate email
    if (!email) {
      return NextResponse.json(
        { success: false, error: "Email is required" },
        { status: 400 }
      );
    }

    const emailRegex = /\S+@\S+\.\S+/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: "Please enter a valid email address" },
        { status: 400 }
      );
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });

    // Always return success to prevent email enumeration attacks
    // But only send email if user exists
    if (user) {
      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString("hex");
      const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      // Save reset token to database
      await prisma.user.update({
        where: { id: user.id },
        data: {
          resetToken,
          resetTokenExpiry,
        },
      });

      // Send password reset email
      try {
        await sendPasswordResetEmail(user.email, user.name, resetToken);
      } catch (error) {
        console.error("Failed to send password reset email:", error);
        // Don't expose email sending errors to prevent information leakage
      }
    }

    const response: ApiResponse = {
      success: true,
      message: "If an account with that email exists, we've sent a password reset link.",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error processing forgot password request:", error);
    return NextResponse.json(
      { success: false, error: "Failed to process request" },
      { status: 500 }
    );
  }
}
