"use client";

import { useSession } from "@/lib/auth-client";
import { UserRoute } from "@/components/auth/protected-route";
import NavBar from "@/components/navbar";
import OrdersContent from "@/components/orders/orders-content";
import { User } from "@/utils/types";

export default function OrdersPage() {
  return (
    <UserRoute>
      <OrdersPageContent />
    </UserRoute>
  );
}

function OrdersPageContent() {
  const { data: session } = useSession();

  // At this point, we know the user is authenticated due to UserRoute
  // Type cast to include the role property that exists at runtime
  const user = session!.user as User;

  return (
    <div className="min-h-screen bg-gray-50">
      <NavBar user={user} />
      <div className="container mx-auto px-4 py-6">
        <OrdersContent />
      </div>
    </div>
  );
}
