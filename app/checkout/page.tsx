"use client";

import { useSession } from "@/lib/auth-client";
import { UserRoute } from "@/components/auth/protected-route";
import NavBar from "@/components/navbar";
import CheckoutContent from "@/components/checkout/checkout-content";
import { useCart } from "@/contexts/cart-context";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ShoppingBag } from "lucide-react";
import Link from "next/link";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { User } from "@/utils/types";

export default function CheckoutPage() {
  return (
    <UserRoute>
      <CheckoutPageContent />
    </UserRoute>
  );
}

function CheckoutPageContent() {
  const { data: session } = useSession();
  const { state: cartState } = useCart();
  const router = useRouter();

  // Redirect to cart if empty
  useEffect(() => {
    if (!cartState.isLoading && cartState.items.length === 0) {
      router.push("/cart");
    }
  }, [cartState.isLoading, cartState.items.length, router]);

  // At this point, we know the user is authenticated due to UserRoute
  // Type cast to include the role property that exists at runtime
  const user = session!.user as User;

  if (cartState.isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavBar user={user} />
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </div>
      </div>
    );
  }

  if (cartState.items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavBar user={user} />
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-2xl mx-auto text-center py-12">
            <div className="mb-6">
              <ShoppingBag className="mx-auto h-16 w-16 text-gray-400" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
            <p className="text-gray-600 mb-8">
              You need items in your cart to proceed to checkout.
            </p>
            <Link href="/products">
              <Button size="lg">
                <ArrowLeft className="mr-2 h-5 w-5" />
                Continue Shopping
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <NavBar user={user} />
      <div className="container mx-auto px-4 py-6">
        <CheckoutContent user={user} />
      </div>
    </div>
  );
}
