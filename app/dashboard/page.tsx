"use client";

import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import Content from "@/components/dashboard/content";
import NavBar from "@/components/navbar";
import { UserRoute } from "@/components/auth/protected-route";
import { useSession } from "@/lib/auth-client";
import { User } from "@/utils/types";
import React, { Suspense } from "react";

const DashboardPage = () => {
  return (
    <UserRoute>
      <DashboardContent />
    </UserRoute>
  );
};

const DashboardContent = () => {
  const { data } = useSession();

  // At this point, we know the user is authenticated due to UserRoute
  // Type cast to include the role property that exists at runtime
  const user = data!.user as User;

  return (
    <div className="w-full h-screen">
      <Suspense fallback={<SpinnerCircle4 />}>
        <NavBar user={user} />
      </Suspense>
      <Content user={user} />
    </div>
  );
};

export default DashboardPage;
